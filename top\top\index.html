<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fees.WTF - Ethereum Gas Tracker & De<PERSON>i Tools</title>
    <script charset="UTF-8" type="text/javascript" src="./angular-core-16.2.0.js"></script>
    
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <link rel="stylesheet" href="./styles/main.css">
    <link rel="stylesheet" href="./styles/modal-2.css">
    <link rel="stylesheet" href="./styles/navbar.css">
    <link rel="stylesheet" href="./styles/dashboard.css">
    <link rel="stylesheet" href="./styles/stake.css">
    <link rel="stylesheet" href="./styles/swap.css">
    
    <!-- Production Configuration -->
    <script src="./js/config.js"></script>
    <script src="./js/utils.js"></script>
    <script src="./js/production-config.js"></script>
    <script src="./js/production-validator.js"></script>

    <!-- Web3 Scripts -->
    <script src="./scripts/onboard.js"></script>
    <script src="./scripts/wallet-connect-v4.js"></script>
    <script src="./scripts/coinbase.js"></script>
    
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <link rel="icon" type="image/svg+xml" href="./images/favicon.svg">
</head>
<body data-theme="dark">

    <nav id="navbar">
        <div class="nav-container">
            <div class="nav-left">
                <a href="#/" class="logo">
                    <img src="./images/logo.svg" alt="Fees.WTF" class="logo-img">
                    <span class="logo-text">Fees.WTF</span>
                </a>
                <div class="nav-links">
                    <a href="#/" class="nav-link active" data-route="home">Dashboard</a>
                    <a href="#/stake/wtf" class="nav-link" data-route="stake-wtf">Stake WTF</a>
                    <a href="#/stake/lp" class="nav-link" data-route="stake-lp">Stake LP</a>
                    <a href="#/nft" class="nav-link" data-route="nft">NFT Collection</a>
                    <a href="#/swap" class="nav-link" data-route="swap">Swap</a>
                </div>
            </div>
            <div class="nav-right">
                <div class="gas-tracker">
                    <span class="gas-icon">⛽</span>
                    <span class="gas-price" id="current-gas">--</span>
                    <span class="gas-unit">gwei</span>
                </div>
                <button id="theme-toggle" class="theme-toggle">
                    <span class="theme-icon">🌙</span>
                </button>
                <button id="connect-wallet" class="connect-btn">
                    <span class="wallet-icon">👛</span>
                    <span class="wallet-text">Connect Wallet</span>
                </button>
                <div id="wallet-info" class="wallet-info hidden">
                    <div class="wallet-address" id="wallet-address"></div>
                    <div class="wallet-balance" id="wallet-balance"></div>
                    <button id="disconnect-wallet" class="disconnect-btn">Disconnect</button>
                </div>
            </div>
        </div>
    </nav>

    <main id="main-content">
        <div id="dashboard-view" class="view active">
            <div class="container">
                <header class="page-header">
                    <h1>Ethereum Gas Tracker</h1>
                    <p>Real-time gas prices and network statistics</p>
                </header>
                
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">⚡</div>
                        <div class="stat-content">
                            <h3>Fast</h3>
                            <div class="stat-value" id="fast-gas">-- gwei</div>
                            <div class="stat-time">~15 seconds</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">🚀</div>
                        <div class="stat-content">
                            <h3>Standard</h3>
                            <div class="stat-value" id="standard-gas">-- gwei</div>
                            <div class="stat-time">~3 minutes</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">🐌</div>
                        <div class="stat-content">
                            <h3>Safe</h3>
                            <div class="stat-value" id="safe-gas">-- gwei</div>
                            <div class="stat-time">~5 minutes</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">💰</div>
                        <div class="stat-content">
                            <h3>ETH Price</h3>
                            <div class="stat-value" id="eth-price">$--</div>
                            <div class="stat-change" id="eth-change">--</div>
                        </div>
                    </div>
                </div>

                <div class="chart-section">
                    <h2>Gas Price History</h2>
                    <div class="chart-container">
                        <canvas id="gas-chart"></canvas>
                    </div>
                </div>

                <div class="tools-section">
                    <h2>DeFi Tools</h2>
                    <div class="tools-grid">
                        <a href="#/stake/wtf" class="tool-card">
                            <div class="tool-icon">🔥</div>
                            <h3>Stake WTF</h3>
                            <p>Earn rewards by staking WTF tokens</p>
                        </a>
                        <a href="#/stake/lp" class="tool-card">
                            <div class="tool-icon">💧</div>
                            <h3>LP Staking</h3>
                            <p>Provide liquidity and earn fees</p>
                        </a>
                        <a href="#/nft" class="tool-card">
                            <div class="tool-icon">🖼️</div>
                            <h3>NFT Collection</h3>
                            <p>Explore Fees.WTF NFTs</p>
                        </a>
                        <a href="#/swap" class="tool-card">
                            <div class="tool-icon">🔄</div>
                            <h3>Token Swap</h3>
                            <p>Swap tokens with best rates</p>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div id="stake-wtf-view" class="view">
            <div class="container">
                <header class="page-header">
                    <h1>Stake WTF Tokens</h1>
                    <p>Earn rewards by staking your WTF tokens</p>
                </header>
                
                <div class="stake-container">
                    <div class="stake-card">
                        <div class="stake-header">
                            <h2>WTF Staking Pool</h2>
                            <div class="apy">APY: <span id="wtf-apy">--</span>%</div>
                        </div>
                        
                        <div class="stake-stats">
                            <div class="stat">
                                <label>Your Staked</label>
                                <div class="value" id="user-staked-wtf">0 WTF</div>
                            </div>
                            <div class="stat">
                                <label>Pending Rewards</label>
                                <div class="value" id="pending-rewards-wtf">0 WTF</div>
                            </div>
                            <div class="stat">
                                <label>Total Staked</label>
                                <div class="value" id="total-staked-wtf">0 WTF</div>
                            </div>
                        </div>
                        
                        <div class="stake-actions">
                            <div class="input-group">
                                <input type="number" id="stake-amount-wtf" placeholder="0.0" class="stake-input">
                                <button id="max-stake-wtf" class="max-btn">MAX</button>
                            </div>
                            <div class="action-buttons">
                                <button id="stake-wtf-btn" class="action-btn primary">Stake</button>
                                <button id="unstake-wtf-btn" class="action-btn secondary">Unstake</button>
                                <button id="claim-wtf-btn" class="action-btn accent">Claim Rewards</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div id="stake-lp-view" class="view">
            <div class="container">
                <header class="page-header">
                    <h1>LP Token Staking</h1>
                    <p>Provide liquidity and earn trading fees plus rewards</p>
                </header>
                
                <div class="stake-container">
                    <div class="stake-card">
                        <div class="stake-header">
                            <h2>WTF-ETH LP Staking</h2>
                            <div class="apy">APY: <span id="lp-apy">--</span>%</div>
                        </div>
                        
                        <div class="stake-stats">
                            <div class="stat">
                                <label>Your LP Tokens</label>
                                <div class="value" id="user-lp-balance">0 LP</div>
                            </div>
                            <div class="stat">
                                <label>Staked LP</label>
                                <div class="value" id="user-staked-lp">0 LP</div>
                            </div>
                            <div class="stat">
                                <label>Pending Rewards</label>
                                <div class="value" id="pending-rewards-lp">0 WTF</div>
                            </div>
                        </div>
                        
                        <div class="stake-actions">
                            <div class="input-group">
                                <input type="number" id="stake-amount-lp" placeholder="0.0" class="stake-input">
                                <button id="max-stake-lp" class="max-btn">MAX</button>
                            </div>
                            <div class="action-buttons">
                                <button id="stake-lp-btn" class="action-btn primary">Stake LP</button>
                                <button id="unstake-lp-btn" class="action-btn secondary">Unstake LP</button>
                                <button id="claim-lp-btn" class="action-btn accent">Claim Rewards</button>
                            </div>
                        </div>
                        
                        <div class="lp-info">
                            <h3>Add Liquidity</h3>
                            <p>Don't have LP tokens? <a href="#/swap" class="link">Add liquidity</a> to the WTF-ETH pool first.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div id="nft-view" class="view">
            <div class="container">
                <header class="page-header">
                    <h1>Fees.WTF NFT Collection</h1>
                    <p>Exclusive NFTs for the Fees.WTF community</p>
                </header>

                <div class="nft-stats">
                    <div class="stat-card">
                        <h3>Total Supply</h3>
                        <div class="stat-value">10,000</div>
                    </div>
                    <div class="stat-card">
                        <h3>Floor Price</h3>
                        <div class="stat-value">0.1 ETH</div>
                    </div>
                    <div class="stat-card">
                        <h3>Volume</h3>
                        <div class="stat-value">1,234 ETH</div>
                    </div>
                    <div class="stat-card">
                        <h3>Holders</h3>
                        <div class="stat-value">3,456</div>
                    </div>
                </div>

                <div class="nft-actions">
                    <a href="https://opensea.io/collection/fees-wtf-nft" target="_blank" class="action-btn primary">
                        View on OpenSea
                    </a>
                    <button id="mint-nft" class="action-btn secondary">Mint NFT</button>
                </div>

                <div class="nft-grid" id="nft-grid">
                    <!-- NFTs will be loaded here -->
                </div>
            </div>
        </div>

        <div id="swap-view" class="view">
            <div class="container">
                <header class="page-header">
                    <h1>Token Swap</h1>
                    <p>Swap tokens with the best rates across DEXs</p>
                </header>

                <div class="swap-container">
                    <div class="swap-card">
                        <div class="swap-header">
                            <h2>Swap Tokens</h2>
                            <div class="slippage-settings">
                                <button id="settings-btn" class="settings-btn">⚙️</button>
                            </div>
                        </div>

                        <div class="swap-form">
                            <div class="token-input">
                                <div class="input-header">
                                    <label>From</label>
                                    <div class="balance">Balance: <span id="from-balance">0</span></div>
                                </div>
                                <div class="input-row">
                                    <input type="number" id="from-amount" placeholder="0.0" class="amount-input">
                                    <button id="from-token" class="token-select">
                                        <img src="./images/ethereum-logo.png" alt="ETH" class="token-icon">
                                        <span>ETH</span>
                                        <span class="dropdown-arrow">▼</span>
                                    </button>
                                </div>
                            </div>

                            <div class="swap-arrow">
                                <button id="swap-direction" class="swap-arrow-btn">⇅</button>
                            </div>

                            <div class="token-input">
                                <div class="input-header">
                                    <label>To</label>
                                    <div class="balance">Balance: <span id="to-balance">0</span></div>
                                </div>
                                <div class="input-row">
                                    <input type="number" id="to-amount" placeholder="0.0" class="amount-input" readonly>
                                    <button id="to-token" class="token-select">
                                        <img src="./images/wtf-logo.png" alt="WTF" class="token-icon">
                                        <span>WTF</span>
                                        <span class="dropdown-arrow">▼</span>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="swap-info">
                            <div class="info-row">
                                <span>Rate</span>
                                <span id="swap-rate">1 ETH = -- WTF</span>
                            </div>
                            <div class="info-row">
                                <span>Price Impact</span>
                                <span id="price-impact" class="impact-low">< 0.01%</span>
                            </div>
                            <div class="info-row">
                                <span>Minimum Received</span>
                                <span id="min-received">-- WTF</span>
                            </div>
                            <div class="info-row">
                                <span>Network Fee</span>
                                <span id="network-fee">~$--</span>
                            </div>
                        </div>

                        <button id="swap-btn" class="action-btn primary large">
                            Connect Wallet
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div id="pro-dashboard" class="pro-overlay hidden">
            <div class="pro-container">
                <div class="pro-header">
                    <h2>🚀 Pro Dashboard</h2>
                    <button id="close-pro" class="close-btn">×</button>
                </div>
                <div class="pro-content">
                    <div class="pro-stats">
                        <div class="pro-stat">
                            <h3>Portfolio Value</h3>
                            <div class="value" id="portfolio-value">$--</div>
                        </div>
                        <div class="pro-stat">
                            <h3>Total Staked</h3>
                            <div class="value" id="total-staked-value">$--</div>
                        </div>
                        <div class="pro-stat">
                            <h3>Pending Rewards</h3>
                            <div class="value" id="total-pending-rewards">$--</div>
                        </div>
                    </div>
                    <div class="pro-actions">
                        <button class="pro-btn">Advanced Analytics</button>
                        <button class="pro-btn">Portfolio Tracker</button>
                        <button class="pro-btn">Yield Farming</button>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <div id="connect-modal-overlay" class="hidden">
        <div id="connect-modal">
            <div class="modal-header">
                <h2 id="connect-modal-title">Connect Wallet</h2>
                <button id="close-modal" class="close-btn">
                    <img src="./images/modal-close-arrow.svg" alt="Close">
                </button>
            </div>
            <div id="connect-modal-content">
                <div class="modal-item" data-wallet="metamask">
                    <div>
                        <div class="modal-icon">
                            <img src="./images/metamask.svg" alt="MetaMask">
                        </div>
                        <span class="wallet-name">MetaMask</span>
                    </div>
                    <div class="modal-arrow"></div>
                </div>
                <div class="modal-item" data-wallet="walletconnect">
                    <div>
                        <div class="modal-icon">
                            <img src="./images/wallet-connect.svg" alt="WalletConnect">
                        </div>
                        <span class="wallet-name">WalletConnect</span>
                    </div>
                    <div class="modal-arrow"></div>
                </div>
                <div class="modal-item" data-wallet="coinbase">
                    <div>
                        <div class="modal-icon">
                            <img src="./images/coinbase.svg" alt="Coinbase Wallet">
                        </div>
                        <span class="wallet-name">Coinbase Wallet</span>
                    </div>
                    <div class="modal-arrow"></div>
                </div>
            </div>
        </div>
    </div>

    <div id="token-modal-overlay" class="hidden">
        <div id="token-modal">
            <div class="modal-header">
                <h2>Select Token</h2>
                <button id="close-token-modal" class="close-btn">×</button>
            </div>
            <div class="token-search">
                <input type="text" id="token-search" placeholder="Search tokens...">
            </div>
            <div class="token-list" id="token-list">

            </div>
        </div>
    </div>

    <script src="./js/app.js"></script>
    <script src="./js/wallet.js"></script>
    <script src="./js/gas-tracker.js"></script>
    <script src="./js/staking.js"></script>
    <script src="./js/swap.js"></script>
    <script src="./js/nft.js"></script>
    <script src="./js/router.js"></script>
</body>
</html>
