# Fees.WTF Production Deployment Guide

This guide covers the complete process of deploying the Fees.WTF application to production with real API integrations and blockchain connectivity.

## 🚀 Pre-Deployment Checklist

### 1. API Keys and Configuration

Before deploying to production, you must configure the following API keys:

#### Required API Keys
- **Etherscan API Key**: For gas price data
  - Get from: https://etherscan.io/apis
  - Set in: `js/production-config.js` or environment variables
  
- **CoinGecko API Key** (Optional but recommended for higher rate limits)
  - Get from: https://www.coingecko.com/en/api
  - Free tier available with rate limits
  
- **OpenSea API Key**: For NFT collection data
  - Get from: https://docs.opensea.io/reference/api-overview
  - Required for production NFT features
  
- **Alchemy API Key**: For blockchain RPC calls
  - Get from: https://www.alchemy.com/
  - Alternative: Infura (https://infura.io/)

#### Optional API Keys (for enhanced features)
- **1inch API Key**: For DEX aggregation
- **CoinMarketCap API Key**: For price data backup
- **0x Protocol**: For swap quotes

### 2. Smart Contract Addresses

Update the contract addresses in `js/production-config.js`:

```javascript
production: {
    wtfToken: '0xYourWTFTokenAddress',
    wtfStaking: '0xYourWTFStakingContract',
    lpToken: '0xYourLPTokenAddress',
    lpStaking: '0xYourLPStakingContract',
    nftCollection: '0xYourNFTCollectionAddress',
    chainId: 1 // Ethereum Mainnet
}
```

### 3. Environment Configuration

#### Option A: Environment Variables (Recommended)
Set the following environment variables:

```bash
export ETHERSCAN_API_KEY="your_etherscan_key"
export OPENSEA_API_KEY="your_opensea_key"
export ALCHEMY_API_KEY="your_alchemy_key"
export INFURA_API_KEY="your_infura_key"
export CMC_API_KEY="your_coinmarketcap_key"
export ONEINCH_API_KEY="your_1inch_key"
```

#### Option B: Direct Configuration
Update the API keys directly in `js/production-config.js` (less secure):

```javascript
getAPIKeys() {
    return {
        etherscan: 'your_etherscan_key',
        opensea: 'your_opensea_key',
        alchemy: 'your_alchemy_key',
        // ... other keys
    };
}
```

## 🔧 Production Setup Steps

### Step 1: Clone and Prepare Files

```bash
# Clone the repository
git clone <your-repo-url>
cd fees-wtf

# Navigate to the application directory
cd top/top

# Verify all files are present
ls -la js/
```

### Step 2: Configure API Keys

Edit `js/production-config.js` and update:
1. Contract addresses for your deployed contracts
2. API keys (or set environment variables)
3. Feature flags as needed

### Step 3: Test in Development Mode

```bash
# Start local development server
python start-server.py
# or
npm start

# Open browser and test all features
# Check browser console for any errors
```

### Step 4: Run Production Validation

Open browser console and run:
```javascript
// This will automatically run in development mode
// Or manually trigger:
window.productionValidator.runFullValidation();
```

Review the validation report and fix any critical errors.

### Step 5: Deploy to Production

#### Option A: Static Hosting (Netlify, Vercel, GitHub Pages)

1. **Netlify Deployment:**
   ```bash
   # Install Netlify CLI
   npm install -g netlify-cli
   
   # Deploy
   netlify deploy --prod --dir=.
   ```

2. **Vercel Deployment:**
   ```bash
   # Install Vercel CLI
   npm install -g vercel
   
   # Deploy
   vercel --prod
   ```

3. **GitHub Pages:**
   - Push code to GitHub repository
   - Enable GitHub Pages in repository settings
   - Set source to main branch / root folder

#### Option B: Traditional Web Hosting

1. Upload all files to your web server
2. Ensure HTTPS is enabled
3. Configure proper MIME types for .js files
4. Set up proper caching headers

### Step 6: Configure Domain and SSL

1. Point your domain to the hosting service
2. Ensure SSL certificate is properly configured
3. Test HTTPS access
4. Update any hardcoded URLs if necessary

## 🔍 Post-Deployment Verification

### 1. Functional Testing

Test all major features:
- [ ] Gas price tracking displays real data
- [ ] ETH price updates correctly
- [ ] Wallet connection works (MetaMask, WalletConnect, Coinbase)
- [ ] Token swap quotes are real and accurate
- [ ] NFT collection loads real data from OpenSea
- [ ] Staking data reflects actual blockchain state
- [ ] All API calls return real data (no mock data)

### 2. Performance Testing

- [ ] Page load time < 3 seconds
- [ ] API response times < 2 seconds
- [ ] No console errors
- [ ] Mobile responsiveness works
- [ ] All images and assets load correctly

### 3. Security Verification

- [ ] HTTPS is enforced
- [ ] No API keys exposed in client-side code
- [ ] CSP headers configured (if applicable)
- [ ] No mixed content warnings

## 🚨 Troubleshooting Common Issues

### API Rate Limiting
**Problem**: API calls failing with 429 errors
**Solution**: 
- Implement API key rotation
- Add longer delays between requests
- Use multiple API providers as fallbacks

### CORS Issues
**Problem**: API calls blocked by CORS policy
**Solution**:
- Use a backend proxy for sensitive API calls
- Configure CORS headers on your server
- Use APIs that support CORS

### Wallet Connection Issues
**Problem**: Wallet not connecting in production
**Solution**:
- Ensure HTTPS is enabled
- Check that wallet extensions are installed
- Verify network configuration matches

### Contract Call Failures
**Problem**: Smart contract calls returning errors
**Solution**:
- Verify contract addresses are correct
- Check network ID matches
- Ensure contracts are deployed and verified

## 📊 Monitoring and Analytics

### 1. Error Tracking

Integrate error tracking service (recommended):

```javascript
// Example: Sentry integration
import * as Sentry from "@sentry/browser";

Sentry.init({
    dsn: "YOUR_SENTRY_DSN",
    environment: "production"
});
```

### 2. Analytics

Add analytics tracking:

```javascript
// Example: Google Analytics
gtag('config', 'GA_MEASUREMENT_ID', {
    page_title: 'Fees.WTF',
    page_location: window.location.href
});
```

### 3. Performance Monitoring

Monitor key metrics:
- API response times
- Page load performance
- User engagement
- Error rates

## 🔄 Maintenance and Updates

### Regular Tasks

1. **API Key Rotation**: Rotate API keys quarterly
2. **Dependency Updates**: Update Chart.js and other dependencies
3. **Contract Updates**: Update contract addresses if redeployed
4. **Performance Monitoring**: Review analytics and optimize slow endpoints

### Emergency Procedures

1. **API Outage**: Fallback to cached data and alternative APIs
2. **Contract Issues**: Display maintenance message and disable affected features
3. **Security Issues**: Immediately update affected code and redeploy

## 📝 Configuration Reference

### Feature Flags

Control features via `js/production-config.js`:

```javascript
getFeatureFlags() {
    return {
        enableRealTimeUpdates: true,    // Real-time data updates
        enableAdvancedCharts: true,     // Chart.js integration
        enableNotifications: true,      // User notifications
        useMultipleGasAPIs: true,      // Fallback gas APIs
        useDEXAggregators: true,       // 1inch/0x integration
        useRealNFTData: true,          // OpenSea integration
        useBlockchainData: true,       // Smart contract calls
    };
}
```

### Rate Limiting Configuration

Adjust API rate limits in `js/config.js`:

```javascript
this.rateLimits = {
    etherscan: { requests: 5, window: 1000 },    // 5 req/sec
    coingecko: { requests: 50, window: 60000 },  // 50 req/min
    opensea: { requests: 4, window: 1000 },      // 4 req/sec
};
```

## 🎯 Success Metrics

Your deployment is successful when:

- ✅ All validation tests pass
- ✅ Real data displays across all features
- ✅ No console errors in production
- ✅ Page loads in < 3 seconds
- ✅ All wallet integrations work
- ✅ Mobile experience is smooth
- ✅ HTTPS is properly configured

## 🆘 Support

If you encounter issues during deployment:

1. Check the browser console for errors
2. Run the production validator
3. Verify all API keys are correctly configured
4. Test individual components in isolation
5. Check network connectivity and CORS settings

For additional support, refer to the main README.md file or create an issue in the repository.

---

**Note**: This deployment guide assumes you have basic knowledge of web deployment and blockchain development. Always test thoroughly in a staging environment before deploying to production.
