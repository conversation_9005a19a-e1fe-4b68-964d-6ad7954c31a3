// NFT Manager for Fees.WTF NFT Collection
class NFTManager {
    constructor() {
        this.nfts = [];
        this.collectionStats = {
            totalSupply: 10000,
            floorPrice: 0.1,
            volume: 1234,
            holders: 3456
        };
        this.userNFTs = [];
        this.isLoading = false;
        this.collectionSlug = 'fees-wtf-nft'; // OpenSea collection slug
        this.contractAddress = '0x...'; // Replace with actual contract address
        this.maxRetries = 3;

        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadCollectionData();
    }

    setupEventListeners() {
        // Mint NFT button
        const mintBtn = document.getElementById('mint-nft');
        if (mintBtn) {
            mintBtn.addEventListener('click', () => this.mintNFT());
        }

        // OpenSea link is handled by the anchor tag directly
    }

    async loadCollectionData() {
        try {
            this.isLoading = true;
            this.showLoadingState();

            utils.startPerformanceTimer('nft-collection-load');

            // Load collection stats and NFTs in parallel
            const [statsResult, nftsResult] = await Promise.allSettled([
                utils.retryOperation(() => this.fetchCollectionStats(), 'nft-stats', this.maxRetries),
                utils.retryOperation(() => this.fetchNFTs(), 'nft-assets', this.maxRetries)
            ]);

            // Handle stats result
            if (statsResult.status === 'fulfilled') {
                this.collectionStats = statsResult.value;
            } else {
                console.warn('Failed to load collection stats:', statsResult.reason);
                await utils.handleError(statsResult.reason, 'nft-stats', false);
            }

            // Handle NFTs result
            if (nftsResult.status === 'fulfilled') {
                this.nfts = nftsResult.value;
            } else {
                console.warn('Failed to load NFTs:', nftsResult.reason);
                await utils.handleError(nftsResult.reason, 'nft-assets', false);
                // Use fallback mock data
                this.nfts = this.generateMockNFTs(12);
            }

            this.updateStatsUI();
            this.renderNFTGrid();

        } catch (error) {
            await utils.handleError(error, 'nft-collection-load');
            this.showError('Failed to load NFT collection');
        } finally {
            this.isLoading = false;
            this.hideLoadingState();
            utils.endPerformanceTimer('nft-collection-load');
        }
    }

    async fetchCollectionStats() {
        try {
            // Try OpenSea API first
            const data = await apiConfig.makeRequest('opensea', `/collection/${this.collectionSlug}`);

            if (!data || !data.collection) {
                throw new Error('Invalid response from OpenSea API');
            }

            const collection = data.collection;
            const stats = collection.stats;

            const result = {
                totalSupply: stats.total_supply || 10000,
                floorPrice: parseFloat(stats.floor_price) || 0.1,
                volume: parseFloat(stats.total_volume) || 1234,
                holders: stats.num_owners || 3456,
                source: 'opensea'
            };

            // Cache the result
            localStorage.setItem('nftCollectionStats', JSON.stringify({
                stats: result,
                timestamp: Date.now()
            }));

            return result;

        } catch (error) {
            console.warn('Failed to fetch from OpenSea, using fallback data:', error);

            // Fallback to cached data or reasonable defaults
            const cached = this.getCachedCollectionStats();
            if (cached) {
                return cached;
            }

            // Ultimate fallback with some variation
            return {
                totalSupply: 10000,
                floorPrice: 0.08 + Math.random() * 0.04, // 0.08 - 0.12 ETH
                volume: 1200 + Math.random() * 100, // 1200-1300 ETH
                holders: 3400 + Math.floor(Math.random() * 100), // 3400-3500
                source: 'fallback'
            };
        }
    }

    getCachedCollectionStats() {
        try {
            const cached = localStorage.getItem('nftCollectionStats');
            if (cached) {
                const data = JSON.parse(cached);
                // Use if less than 1 hour old
                if (Date.now() - data.timestamp < 3600000) {
                    return data.stats;
                }
            }
        } catch (error) {
            console.warn('Failed to load cached collection stats:', error);
        }
        return null;
    }

    async fetchNFTs() {
        try {
            // Try OpenSea API first
            const data = await apiConfig.makeRequest('opensea', apiConfig.endpoints.opensea.assets, {
                params: {
                    collection: this.collectionSlug,
                    order_direction: 'desc',
                    offset: 0,
                    limit: 20
                }
            });

            if (!data || !data.assets) {
                throw new Error('Invalid response from OpenSea API');
            }

            const nfts = data.assets.map(asset => ({
                tokenId: asset.token_id,
                name: asset.name || `${this.collectionSlug} #${asset.token_id}`,
                image: asset.image_url || asset.image_preview_url || this.generateNFTImage(asset.token_id),
                price: asset.last_sale ? parseFloat(asset.last_sale.total_price) / Math.pow(10, 18) : null,
                trait: this.extractMainTrait(asset.traits),
                rarity: this.calculateTraitRarity(asset.traits),
                owner: asset.owner?.address || 'Unknown',
                listed: asset.sell_orders && asset.sell_orders.length > 0,
                description: asset.description,
                permalink: asset.permalink,
                source: 'opensea'
            }));

            // Cache the result
            localStorage.setItem('nftAssets', JSON.stringify({
                assets: nfts,
                timestamp: Date.now()
            }));

            return nfts;

        } catch (error) {
            console.warn('Failed to fetch NFTs from OpenSea, using fallback data:', error);

            // Try cached data first
            const cached = this.getCachedNFTs();
            if (cached) {
                return cached;
            }

            // Ultimate fallback to mock data
            return this.generateMockNFTs(12);
        }
    }

    getCachedNFTs() {
        try {
            const cached = localStorage.getItem('nftAssets');
            if (cached) {
                const data = JSON.parse(cached);
                // Use if less than 30 minutes old
                if (Date.now() - data.timestamp < 1800000) {
                    return data.assets;
                }
            }
        } catch (error) {
            console.warn('Failed to load cached NFT assets:', error);
        }
        return null;
    }

    extractMainTrait(traits) {
        if (!traits || traits.length === 0) return 'Common';

        // Find the most significant trait (lowest trait_count usually means rarer)
        const sortedTraits = traits.sort((a, b) => (a.trait_count || 1000) - (b.trait_count || 1000));
        return sortedTraits[0]?.value || 'Common';
    }

    calculateTraitRarity(traits) {
        if (!traits || traits.length === 0) return 50;

        // Calculate rarity based on trait counts
        const totalSupply = this.collectionStats.totalSupply || 10000;
        const rarities = traits.map(trait => {
            const traitCount = trait.trait_count || totalSupply * 0.5;
            return (traitCount / totalSupply) * 100;
        });

        // Return the rarest trait percentage
        return Math.min(...rarities);
    }

    generateMockNFTs(count) {
        const nfts = [];
        const traits = [
            'Rare', 'Epic', 'Legendary', 'Common', 'Uncommon',
            'Mystic', 'Divine', 'Cosmic', 'Ethereal', 'Quantum'
        ];

        for (let i = 0; i < count; i++) {
            const tokenId = Math.floor(Math.random() * 10000) + 1;
            const trait = traits[Math.floor(Math.random() * traits.length)];
            const price = 0.05 + Math.random() * 0.5; // 0.05 - 0.55 ETH

            nfts.push({
                tokenId: tokenId,
                name: `Fees.WTF #${tokenId}`,
                image: this.generateNFTImage(tokenId),
                price: price,
                trait: trait,
                rarity: this.calculateRarity(trait),
                owner: this.generateRandomAddress(),
                listed: Math.random() > 0.7 // 30% chance of being listed
            });
        }

        return nfts.sort((a, b) => a.tokenId - b.tokenId);
    }

    generateNFTImage(tokenId) {
        // Generate a placeholder image URL
        // In real implementation, this would be actual NFT images
        const colors = ['FF6B6B', '4ECDC4', '45B7D1', 'FFA07A', '98D8C8', 'F7DC6F'];
        const color = colors[tokenId % colors.length];
        return `https://via.placeholder.com/300x300/${color}/FFFFFF?text=WTF%20%23${tokenId}`;
    }

    calculateRarity(trait) {
        const rarityMap = {
            'Common': 40,
            'Uncommon': 25,
            'Rare': 20,
            'Epic': 10,
            'Legendary': 3,
            'Mystic': 1.5,
            'Divine': 0.4,
            'Cosmic': 0.08,
            'Ethereal': 0.02,
            'Quantum': 0.001
        };
        return rarityMap[trait] || 50;
    }

    generateRandomAddress() {
        return '0x' + Array.from({length: 40}, () => 
            Math.floor(Math.random() * 16).toString(16)
        ).join('');
    }

    updateStatsUI() {
        const statsElements = {
            totalSupply: document.querySelector('.nft-stats .stat-card:nth-child(1) .stat-value'),
            floorPrice: document.querySelector('.nft-stats .stat-card:nth-child(2) .stat-value'),
            volume: document.querySelector('.nft-stats .stat-card:nth-child(3) .stat-value'),
            holders: document.querySelector('.nft-stats .stat-card:nth-child(4) .stat-value')
        };

        if (statsElements.totalSupply) {
            statsElements.totalSupply.textContent = this.collectionStats.totalSupply.toLocaleString();
        }
        if (statsElements.floorPrice) {
            statsElements.floorPrice.textContent = `${this.collectionStats.floorPrice.toFixed(3)} ETH`;
        }
        if (statsElements.volume) {
            statsElements.volume.textContent = `${Math.floor(this.collectionStats.volume)} ETH`;
        }
        if (statsElements.holders) {
            statsElements.holders.textContent = this.collectionStats.holders.toLocaleString();
        }
    }

    renderNFTGrid() {
        const nftGrid = document.getElementById('nft-grid');
        if (!nftGrid) return;

        nftGrid.innerHTML = '';

        this.nfts.forEach(nft => {
            const nftCard = this.createNFTCard(nft);
            nftGrid.appendChild(nftCard);
        });
    }

    createNFTCard(nft) {
        const card = document.createElement('div');
        card.className = 'nft-card';
        
        const isOwned = this.userNFTs.some(userNft => userNft.tokenId === nft.tokenId);
        const statusBadge = isOwned ? 
            '<div class="nft-status owned">Owned</div>' : 
            nft.listed ? '<div class="nft-status listed">Listed</div>' : '';

        card.innerHTML = `
            <div class="nft-image-container">
                <img src="${nft.image}" alt="${nft.name}" class="nft-image" loading="lazy">
                ${statusBadge}
                <div class="nft-rarity ${this.getRarityClass(nft.trait)}">${nft.trait}</div>
            </div>
            <div class="nft-info">
                <div class="nft-name">${nft.name}</div>
                <div class="nft-details">
                    <div class="nft-trait">Trait: ${nft.trait}</div>
                    <div class="nft-rarity-percent">${nft.rarity}% rarity</div>
                </div>
                ${nft.listed ? `
                    <div class="nft-price-section">
                        <div class="nft-price">${nft.price.toFixed(3)} ETH</div>
                        <div class="nft-price-usd">$${(nft.price * (window.app?.ethPrice?.usd || 2500)).toFixed(0)}</div>
                    </div>
                    <button class="nft-buy-btn" onclick="nftManager.buyNFT(${nft.tokenId})">
                        ${isOwned ? 'Owned' : 'Buy Now'}
                    </button>
                ` : `
                    <div class="nft-owner">
                        <small>Owner: ${this.formatAddress(nft.owner)}</small>
                    </div>
                `}
            </div>
        `;

        // Add click handler for NFT details
        card.addEventListener('click', (e) => {
            if (!e.target.classList.contains('nft-buy-btn')) {
                this.showNFTDetails(nft);
            }
        });

        return card;
    }

    getRarityClass(trait) {
        const rarityClasses = {
            'Common': 'common',
            'Uncommon': 'uncommon',
            'Rare': 'rare',
            'Epic': 'epic',
            'Legendary': 'legendary',
            'Mystic': 'mystic',
            'Divine': 'divine',
            'Cosmic': 'cosmic',
            'Ethereal': 'ethereal',
            'Quantum': 'quantum'
        };
        return rarityClasses[trait] || 'common';
    }

    formatAddress(address) {
        return `${address.slice(0, 6)}...${address.slice(-4)}`;
    }

    async mintNFT() {
        if (!window.app || !window.app.walletConnected) {
            window.app.showWalletModal();
            return;
        }

        try {
            const mintBtn = document.getElementById('mint-nft');
            if (mintBtn) {
                mintBtn.innerHTML = '<span class="spinner"></span> Minting...';
                mintBtn.disabled = true;
            }

            // Simulate minting transaction
            await this.simulateMintTransaction();

            // Generate new NFT for user
            const newNFT = this.generateMockNFTs(1)[0];
            newNFT.owner = window.app.currentAccount;
            this.userNFTs.push(newNFT);
            this.nfts.unshift(newNFT);

            this.renderNFTGrid();
            this.showSuccess(`Successfully minted ${newNFT.name}!`);

        } catch (error) {
            console.error('Minting failed:', error);
            this.showError('Minting failed: ' + error.message);
        } finally {
            const mintBtn = document.getElementById('mint-nft');
            if (mintBtn) {
                mintBtn.textContent = 'Mint NFT';
                mintBtn.disabled = false;
            }
        }
    }

    async buyNFT(tokenId) {
        if (!window.app || !window.app.walletConnected) {
            window.app.showWalletModal();
            return;
        }

        const nft = this.nfts.find(n => n.tokenId === tokenId);
        if (!nft || !nft.listed) {
            this.showError('NFT not available for purchase');
            return;
        }

        try {
            // Simulate purchase transaction
            await this.simulatePurchaseTransaction(nft);

            // Update NFT ownership
            nft.owner = window.app.currentAccount;
            nft.listed = false;
            this.userNFTs.push(nft);

            this.renderNFTGrid();
            this.showSuccess(`Successfully purchased ${nft.name} for ${nft.price.toFixed(3)} ETH!`);

        } catch (error) {
            console.error('Purchase failed:', error);
            this.showError('Purchase failed: ' + error.message);
        }
    }

    showNFTDetails(nft) {
        // Create and show NFT details modal
        const modal = this.createNFTDetailsModal(nft);
        document.body.appendChild(modal);
    }

    createNFTDetailsModal(nft) {
        const modal = document.createElement('div');
        modal.className = 'nft-details-modal';
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(5px);
            z-index: 2000;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 1rem;
        `;

        const isOwned = this.userNFTs.some(userNft => userNft.tokenId === nft.tokenId);

        modal.innerHTML = `
            <div style="
                background-color: var(--bg-primary);
                border: 1px solid var(--border-color);
                border-radius: 1rem;
                max-width: 500px;
                width: 100%;
                max-height: 80vh;
                overflow-y: auto;
            ">
                <div style="
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    padding: 1.5rem;
                    border-bottom: 1px solid var(--border-color);
                ">
                    <h2 style="margin: 0; color: var(--text-primary);">${nft.name}</h2>
                    <button onclick="this.closest('.nft-details-modal').remove()" style="
                        width: 32px;
                        height: 32px;
                        border: none;
                        border-radius: 0.375rem;
                        background-color: var(--bg-tertiary);
                        color: var(--text-secondary);
                        cursor: pointer;
                        font-size: 1.125rem;
                    ">×</button>
                </div>
                <div style="padding: 1.5rem;">
                    <img src="${nft.image}" alt="${nft.name}" style="
                        width: 100%;
                        border-radius: 0.75rem;
                        margin-bottom: 1rem;
                    ">
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin-bottom: 1rem;">
                        <div>
                            <label style="font-size: 0.875rem; color: var(--text-secondary);">Token ID</label>
                            <div style="font-weight: 600; color: var(--text-primary);">#${nft.tokenId}</div>
                        </div>
                        <div>
                            <label style="font-size: 0.875rem; color: var(--text-secondary);">Trait</label>
                            <div style="font-weight: 600; color: var(--text-primary);">${nft.trait}</div>
                        </div>
                        <div>
                            <label style="font-size: 0.875rem; color: var(--text-secondary);">Rarity</label>
                            <div style="font-weight: 600; color: var(--text-primary);">${nft.rarity}%</div>
                        </div>
                        <div>
                            <label style="font-size: 0.875rem; color: var(--text-secondary);">Owner</label>
                            <div style="font-weight: 600; color: var(--text-primary); font-family: monospace; font-size: 0.75rem;">
                                ${this.formatAddress(nft.owner)}
                            </div>
                        </div>
                    </div>
                    ${nft.listed && !isOwned ? `
                        <div style="
                            background-color: var(--bg-tertiary);
                            border-radius: 0.75rem;
                            padding: 1rem;
                            margin-bottom: 1rem;
                        ">
                            <div style="font-size: 1.25rem; font-weight: 700; color: var(--text-primary);">
                                ${nft.price.toFixed(3)} ETH
                            </div>
                            <div style="font-size: 0.875rem; color: var(--text-secondary);">
                                $${(nft.price * (window.app?.ethPrice?.usd || 2500)).toFixed(0)} USD
                            </div>
                        </div>
                        <button onclick="nftManager.buyNFT(${nft.tokenId}); this.closest('.nft-details-modal').remove();" style="
                            width: 100%;
                            padding: 1rem;
                            background-color: var(--btn-primary);
                            color: white;
                            border: none;
                            border-radius: 0.5rem;
                            font-size: 1rem;
                            font-weight: 600;
                            cursor: pointer;
                        ">Buy Now</button>
                    ` : isOwned ? `
                        <div style="
                            background-color: rgba(16, 185, 129, 0.1);
                            border: 1px solid var(--success);
                            border-radius: 0.75rem;
                            padding: 1rem;
                            text-align: center;
                            color: var(--success);
                            font-weight: 600;
                        ">You own this NFT!</div>
                    ` : `
                        <div style="
                            background-color: var(--bg-tertiary);
                            border-radius: 0.75rem;
                            padding: 1rem;
                            text-align: center;
                            color: var(--text-secondary);
                        ">Not currently listed for sale</div>
                    `}
                </div>
            </div>
        `;

        // Close on overlay click
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.remove();
            }
        });

        return modal;
    }

    async simulateMintTransaction() {
        return new Promise((resolve, reject) => {
            setTimeout(() => {
                if (Math.random() < 0.1) {
                    reject(new Error('Transaction failed'));
                } else {
                    resolve();
                }
            }, 3000 + Math.random() * 2000);
        });
    }

    async simulatePurchaseTransaction(nft) {
        return new Promise((resolve, reject) => {
            setTimeout(() => {
                if (Math.random() < 0.05) {
                    reject(new Error('Transaction failed'));
                } else {
                    resolve();
                }
            }, 2000 + Math.random() * 2000);
        });
    }

    showLoadingState() {
        const nftGrid = document.getElementById('nft-grid');
        if (nftGrid) {
            nftGrid.innerHTML = '<div class="loading"><div class="spinner"></div></div>';
        }
    }

    hideLoadingState() {
        // Loading state is replaced by actual content
    }

    showSuccess(message) {
        this.createNotification(message, 'success');
    }

    showError(message) {
        this.createNotification(message, 'error');
    }

    createNotification(message, type) {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.style.cssText = `
            position: fixed;
            top: 100px;
            right: 20px;
            background-color: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-left: 4px solid var(--${type === 'success' ? 'success' : 'error'});
            border-radius: 0.5rem;
            padding: 1rem;
            box-shadow: var(--shadow-lg);
            z-index: 10000;
            max-width: 300px;
            color: var(--text-primary);
        `;

        notification.textContent = message;
        document.body.appendChild(notification);

        setTimeout(() => {
            notification.remove();
        }, 5000);
    }

    // Public methods
    loadNFTs() {
        this.loadCollectionData();
    }

    getUserNFTs() {
        return [...this.userNFTs];
    }

    getCollectionStats() {
        return { ...this.collectionStats };
    }
}

// Make NFTManager available globally
window.NFTManager = NFTManager;

// Create global instance for onclick handlers
let nftManager;
