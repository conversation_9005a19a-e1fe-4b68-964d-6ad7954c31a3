class StakingManager {
    constructor() {
        this.wtfStakingData = {
            userStaked: 0,
            pendingRewards: 0,
            totalStaked: 0,
            apy: 0,
            userBalance: 0
        };

        this.lpStakingData = {
            userLPBalance: 0,
            userStaked: 0,
            pendingRewards: 0,
            totalStaked: 0,
            apy: 0
        };

        // Smart contract addresses (replace with actual addresses)
        this.contracts = {
            wtfToken: '0xA68Dd8cB83097765263AdAD881Af6eeD479c4a33',
            wtfStaking: '0x...', // WTF staking contract
            lpToken: '0x...', // LP token contract
            lpStaking: '0x...', // LP staking contract
        };

        this.isLoading = false;
        this.maxRetries = 3;
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadStakingData();
    }

    setupEventListeners() {
        const maxStakeWTF = document.getElementById('max-stake-wtf');
        const stakeWTFBtn = document.getElementById('stake-wtf-btn');
        const unstakeWTFBtn = document.getElementById('unstake-wtf-btn');
        const claimWTFBtn = document.getElementById('claim-wtf-btn');

        if (maxStakeWTF) {
            maxStakeWTF.addEventListener('click', () => this.setMaxStakeAmount('wtf'));
        }
        if (stakeWTFBtn) {
            stakeWTFBtn.addEventListener('click', () => this.stakeWTF());
        }
        if (unstakeWTFBtn) {
            unstakeWTFBtn.addEventListener('click', () => this.unstakeWTF());
        }
        if (claimWTFBtn) {
            claimWTFBtn.addEventListener('click', () => this.claimWTFRewards());
        }

        const maxStakeLP = document.getElementById('max-stake-lp');
        const stakeLPBtn = document.getElementById('stake-lp-btn');
        const unstakeLPBtn = document.getElementById('unstake-lp-btn');
        const claimLPBtn = document.getElementById('claim-lp-btn');

        if (maxStakeLP) {
            maxStakeLP.addEventListener('click', () => this.setMaxStakeAmount('lp'));
        }
        if (stakeLPBtn) {
            stakeLPBtn.addEventListener('click', () => this.stakeLP());
        }
        if (unstakeLPBtn) {
            unstakeLPBtn.addEventListener('click', () => this.unstakeLP());
        }
        if (claimLPBtn) {
            claimLPBtn.addEventListener('click', () => this.claimLPRewards());
        }

        const stakeAmountWTF = document.getElementById('stake-amount-wtf');
        const stakeAmountLP = document.getElementById('stake-amount-lp');

        if (stakeAmountWTF) {
            stakeAmountWTF.addEventListener('input', (e) => this.validateInput(e.target, 'wtf'));
        }
        if (stakeAmountLP) {
            stakeAmountLP.addEventListener('input', (e) => this.validateInput(e.target, 'lp'));
        }
    }

    async loadStakingData() {
        if (!window.app || !window.app.walletConnected) {
            this.updateStakingUI();
            return;
        }

        try {
            this.isLoading = true;
            this.showLoadingState();

            utils.startPerformanceTimer('staking-data-load');

            // Load staking data with retry mechanism
            const [wtfResult, lpResult] = await Promise.allSettled([
                utils.retryOperation(() => this.fetchWTFStakingData(), 'wtf-staking-data', this.maxRetries),
                utils.retryOperation(() => this.fetchLPStakingData(), 'lp-staking-data', this.maxRetries)
            ]);

            // Handle WTF staking data
            if (wtfResult.status === 'fulfilled') {
                this.wtfStakingData = wtfResult.value;
            } else {
                console.warn('Failed to load WTF staking data:', wtfResult.reason);
                await utils.handleError(wtfResult.reason, 'wtf-staking-data', false);
                // Use cached or default data
                this.wtfStakingData = this.getCachedStakingData('wtf') || this.getDefaultWTFData();
            }

            // Handle LP staking data
            if (lpResult.status === 'fulfilled') {
                this.lpStakingData = lpResult.value;
            } else {
                console.warn('Failed to load LP staking data:', lpResult.reason);
                await utils.handleError(lpResult.reason, 'lp-staking-data', false);
                // Use cached or default data
                this.lpStakingData = this.getCachedStakingData('lp') || this.getDefaultLPData();
            }

            this.updateStakingUI();

        } catch (error) {
            await utils.handleError(error, 'staking-data-load');
            this.showError('Failed to load staking data');
        } finally {
            this.isLoading = false;
            this.hideLoadingState();
            utils.endPerformanceTimer('staking-data-load');
        }
    }

    async fetchWTFStakingData() {
        const userAddress = window.app.currentAccount;

        try {
            // Fetch data from smart contracts in parallel
            const [userStaked, pendingRewards, totalStaked, userBalance, apy] = await Promise.all([
                this.getUserStakedWTF(userAddress),
                this.getPendingWTFRewards(userAddress),
                this.getTotalStakedWTF(),
                this.getWTFBalance(userAddress),
                this.getWTFStakingAPY()
            ]);

            const result = {
                userStaked,
                pendingRewards,
                totalStaked,
                apy,
                userBalance,
                source: 'blockchain'
            };

            // Cache the result
            this.cacheStakingData('wtf', result);

            return result;

        } catch (error) {
            console.warn('Failed to fetch WTF staking data from blockchain:', error);
            throw error;
        }
    }

    async getUserStakedWTF(userAddress) {
        try {
            // Call staking contract's balanceOf or stakedAmount function
            const data = '0x70a08231' + userAddress.slice(2).padStart(64, '0'); // balanceOf signature

            const result = await window.ethereum.request({
                method: 'eth_call',
                params: [{
                    to: this.contracts.wtfStaking,
                    data: data
                }, 'latest']
            });

            return parseInt(result, 16) / Math.pow(10, 18); // Convert from wei
        } catch (error) {
            console.warn('Failed to get user staked WTF:', error);
            return 0;
        }
    }

    async getPendingWTFRewards(userAddress) {
        try {
            // Call staking contract's pendingRewards function
            const data = '0xf40f0f52' + userAddress.slice(2).padStart(64, '0'); // pendingRewards signature

            const result = await window.ethereum.request({
                method: 'eth_call',
                params: [{
                    to: this.contracts.wtfStaking,
                    data: data
                }, 'latest']
            });

            return parseInt(result, 16) / Math.pow(10, 18); // Convert from wei
        } catch (error) {
            console.warn('Failed to get pending WTF rewards:', error);
            return 0;
        }
    }

    async getTotalStakedWTF() {
        try {
            // Call staking contract's totalSupply function
            const data = '0x18160ddd'; // totalSupply signature

            const result = await window.ethereum.request({
                method: 'eth_call',
                params: [{
                    to: this.contracts.wtfStaking,
                    data: data
                }, 'latest']
            });

            return parseInt(result, 16) / Math.pow(10, 18); // Convert from wei
        } catch (error) {
            console.warn('Failed to get total staked WTF:', error);
            return 1000000; // Fallback value
        }
    }

    async getWTFBalance(userAddress) {
        try {
            // Call WTF token contract's balanceOf function
            const data = '0x70a08231' + userAddress.slice(2).padStart(64, '0'); // balanceOf signature

            const result = await window.ethereum.request({
                method: 'eth_call',
                params: [{
                    to: this.contracts.wtfToken,
                    data: data
                }, 'latest']
            });

            return parseInt(result, 16) / Math.pow(10, 18); // Convert from wei
        } catch (error) {
            console.warn('Failed to get WTF balance:', error);
            return 0;
        }
    }

    async getWTFStakingAPY() {
        try {
            // This would typically come from a DeFi data API or be calculated
            // For now, we'll use a reasonable estimate
            const data = await apiConfig.makeRequest('dexscreener', '/tokens/' + this.contracts.wtfToken);

            if (data && data.pairs && data.pairs.length > 0) {
                // Calculate APY based on trading volume and liquidity
                const pair = data.pairs[0];
                const volume24h = parseFloat(pair.volume?.h24 || 0);
                const liquidity = parseFloat(pair.liquidity?.usd || 1);

                // Simple APY calculation (this would be more complex in reality)
                const estimatedAPY = Math.min(100, (volume24h / liquidity) * 365 * 0.003 * 100); // 0.3% fee assumption
                return Math.max(5, estimatedAPY); // Minimum 5% APY
            }

            return 15; // Default APY
        } catch (error) {
            console.warn('Failed to calculate WTF staking APY:', error);
            return 15; // Fallback APY
        }
    }

    async fetchLPStakingData() {
        const userAddress = window.app.currentAccount;

        try {
            // Fetch LP staking data from smart contracts
            const [userLPBalance, userStaked, pendingRewards, totalStaked, apy] = await Promise.all([
                this.getLPBalance(userAddress),
                this.getUserStakedLP(userAddress),
                this.getPendingLPRewards(userAddress),
                this.getTotalStakedLP(),
                this.getLPStakingAPY()
            ]);

            const result = {
                userLPBalance,
                userStaked,
                pendingRewards,
                totalStaked,
                apy,
                source: 'blockchain'
            };

            // Cache the result
            this.cacheStakingData('lp', result);

            return result;

        } catch (error) {
            console.warn('Failed to fetch LP staking data from blockchain:', error);
            throw error;
        }
    }

    async getLPBalance(userAddress) {
        try {
            // Call LP token contract's balanceOf function
            const data = '0x70a08231' + userAddress.slice(2).padStart(64, '0');

            const result = await window.ethereum.request({
                method: 'eth_call',
                params: [{
                    to: this.contracts.lpToken,
                    data: data
                }, 'latest']
            });

            return parseInt(result, 16) / Math.pow(10, 18);
        } catch (error) {
            console.warn('Failed to get LP balance:', error);
            return 0;
        }
    }

    async getUserStakedLP(userAddress) {
        try {
            // Call LP staking contract's balanceOf function
            const data = '0x70a08231' + userAddress.slice(2).padStart(64, '0');

            const result = await window.ethereum.request({
                method: 'eth_call',
                params: [{
                    to: this.contracts.lpStaking,
                    data: data
                }, 'latest']
            });

            return parseInt(result, 16) / Math.pow(10, 18);
        } catch (error) {
            console.warn('Failed to get user staked LP:', error);
            return 0;
        }
    }

    async getPendingLPRewards(userAddress) {
        try {
            // Call LP staking contract's pendingRewards function
            const data = '0xf40f0f52' + userAddress.slice(2).padStart(64, '0');

            const result = await window.ethereum.request({
                method: 'eth_call',
                params: [{
                    to: this.contracts.lpStaking,
                    data: data
                }, 'latest']
            });

            return parseInt(result, 16) / Math.pow(10, 18);
        } catch (error) {
            console.warn('Failed to get pending LP rewards:', error);
            return 0;
        }
    }

    async getTotalStakedLP() {
        try {
            // Call LP staking contract's totalSupply function
            const data = '0x18160ddd';

            const result = await window.ethereum.request({
                method: 'eth_call',
                params: [{
                    to: this.contracts.lpStaking,
                    data: data
                }, 'latest']
            });

            return parseInt(result, 16) / Math.pow(10, 18);
        } catch (error) {
            console.warn('Failed to get total staked LP:', error);
            return 50000; // Fallback value
        }
    }

    async getLPStakingAPY() {
        try {
            // Calculate LP staking APY based on trading fees and rewards
            // This would typically involve complex calculations
            return 25; // Placeholder APY
        } catch (error) {
            console.warn('Failed to calculate LP staking APY:', error);
            return 25; // Fallback APY
        }
    }

    // Cache management methods
    cacheStakingData(type, data) {
        try {
            const cacheKey = `stakingData_${type}`;
            localStorage.setItem(cacheKey, JSON.stringify({
                data,
                timestamp: Date.now()
            }));
        } catch (error) {
            console.warn('Failed to cache staking data:', error);
        }
    }

    getCachedStakingData(type) {
        try {
            const cacheKey = `stakingData_${type}`;
            const cached = localStorage.getItem(cacheKey);
            if (cached) {
                const { data, timestamp } = JSON.parse(cached);
                // Use if less than 5 minutes old
                if (Date.now() - timestamp < 300000) {
                    return data;
                }
            }
        } catch (error) {
            console.warn('Failed to load cached staking data:', error);
        }
        return null;
    }

    getDefaultWTFData() {
        return {
            userStaked: 0,
            pendingRewards: 0,
            totalStaked: 1000000,
            apy: 15,
            userBalance: 0,
            source: 'default'
        };
    }

    getDefaultLPData() {
        return {
            userLPBalance: 0,
            userStaked: 0,
            pendingRewards: 0,
            totalStaked: 50000,
            apy: 25,
            source: 'default'
        };
    }

    updateStakingUI() {
        this.updateWTFStakingUI();
        this.updateLPStakingUI();
    }

    updateWTFStakingUI() {
        const elements = {
            apy: document.getElementById('wtf-apy'),
            userStaked: document.getElementById('user-staked-wtf'),
            pendingRewards: document.getElementById('pending-rewards-wtf'),
            totalStaked: document.getElementById('total-staked-wtf')
        };

        if (elements.apy) {
            elements.apy.textContent = this.wtfStakingData.apy.toFixed(1);
        }
        if (elements.userStaked) {
            elements.userStaked.textContent = `${this.formatNumber(this.wtfStakingData.userStaked)} WTF`;
        }
        if (elements.pendingRewards) {
            elements.pendingRewards.textContent = `${this.formatNumber(this.wtfStakingData.pendingRewards)} WTF`;
        }
        if (elements.totalStaked) {
            elements.totalStaked.textContent = `${this.formatNumber(this.wtfStakingData.totalStaked)} WTF`;
        }
    }

    updateLPStakingUI() {
        const elements = {
            apy: document.getElementById('lp-apy'),
            userLPBalance: document.getElementById('user-lp-balance'),
            userStaked: document.getElementById('user-staked-lp'),
            pendingRewards: document.getElementById('pending-rewards-lp')
        };

        if (elements.apy) {
            elements.apy.textContent = this.lpStakingData.apy.toFixed(1);
        }
        if (elements.userLPBalance) {
            elements.userLPBalance.textContent = `${this.formatNumber(this.lpStakingData.userLPBalance)} LP`;
        }
        if (elements.userStaked) {
            elements.userStaked.textContent = `${this.formatNumber(this.lpStakingData.userStaked)} LP`;
        }
        if (elements.pendingRewards) {
            elements.pendingRewards.textContent = `${this.formatNumber(this.lpStakingData.pendingRewards)} WTF`;
        }
    }

    setMaxStakeAmount(type) {
        const inputId = type === 'wtf' ? 'stake-amount-wtf' : 'stake-amount-lp';
        const input = document.getElementById(inputId);
        
        if (!input) return;

        let maxAmount;
        if (type === 'wtf') {
            maxAmount = this.wtfStakingData.userBalance;
        } else {
            maxAmount = this.lpStakingData.userLPBalance;
        }

        input.value = maxAmount.toFixed(6);
        this.validateInput(input, type);
    }

    validateInput(input, type) {
        const value = parseFloat(input.value) || 0;
        let maxAmount;
        
        if (type === 'wtf') {
            maxAmount = this.wtfStakingData.userBalance;
        } else {
            maxAmount = this.lpStakingData.userLPBalance;
        }

        if (value > maxAmount) {
            input.style.borderColor = 'var(--error)';
        } else if (value > 0) {
            input.style.borderColor = 'var(--success)';
        } else {
            input.style.borderColor = 'var(--border-color)';
        }
    }

    async stakeWTF() {
        if (!this.checkWalletConnection()) return;

        const amountInput = document.getElementById('stake-amount-wtf');
        const amount = parseFloat(amountInput.value) || 0;

        if (amount <= 0) {
            this.showError('Please enter a valid amount');
            return;
        }

        if (amount > this.wtfStakingData.userBalance) {
            this.showError('Insufficient balance');
            return;
        }

        try {
            this.setButtonLoading('stake-wtf-btn', true);
            
            await this.simulateTransaction('Staking WTF tokens...');
            
            this.wtfStakingData.userStaked += amount;
            this.wtfStakingData.userBalance -= amount;
            this.wtfStakingData.totalStaked += amount;
            
            this.updateWTFStakingUI();
            amountInput.value = '';
            
            this.showSuccess(`Successfully staked ${amount.toFixed(2)} WTF tokens`);

        } catch (error) {
            console.error('Staking failed:', error);
            this.showError('Staking failed: ' + error.message);
        } finally {
            this.setButtonLoading('stake-wtf-btn', false);
        }
    }

    async unstakeWTF() {
        if (!this.checkWalletConnection()) return;

        const amountInput = document.getElementById('stake-amount-wtf');
        const amount = parseFloat(amountInput.value) || 0;

        if (amount <= 0) {
            this.showError('Please enter a valid amount');
            return;
        }

        if (amount > this.wtfStakingData.userStaked) {
            this.showError('Insufficient staked amount');
            return;
        }

        try {
            this.setButtonLoading('unstake-wtf-btn', true);
            
            await this.simulateTransaction('Unstaking WTF tokens...');
            
            this.wtfStakingData.userStaked -= amount;
            this.wtfStakingData.userBalance += amount;
            this.wtfStakingData.totalStaked -= amount;
            
            this.updateWTFStakingUI();
            amountInput.value = '';
            
            this.showSuccess(`Successfully unstaked ${amount.toFixed(2)} WTF tokens`);

        } catch (error) {
            console.error('Unstaking failed:', error);
            this.showError('Unstaking failed: ' + error.message);
        } finally {
            this.setButtonLoading('unstake-wtf-btn', false);
        }
    }

    async claimWTFRewards() {
        if (!this.checkWalletConnection()) return;

        if (this.wtfStakingData.pendingRewards <= 0) {
            this.showError('No rewards to claim');
            return;
        }

        try {
            this.setButtonLoading('claim-wtf-btn', true);
            
            await this.simulateTransaction('Claiming WTF rewards...');
            
            const rewards = this.wtfStakingData.pendingRewards;
            this.wtfStakingData.pendingRewards = 0;
            this.wtfStakingData.userBalance += rewards;
            
            this.updateWTFStakingUI();
            
            this.showSuccess(`Successfully claimed ${rewards.toFixed(2)} WTF rewards`);

        } catch (error) {
            console.error('Claiming failed:', error);
            this.showError('Claiming failed: ' + error.message);
        } finally {
            this.setButtonLoading('claim-wtf-btn', false);
        }
    }

    async stakeLP() {
        if (!this.checkWalletConnection()) return;

        const amountInput = document.getElementById('stake-amount-lp');
        const amount = parseFloat(amountInput.value) || 0;

        if (amount <= 0 || amount > this.lpStakingData.userLPBalance) {
            this.showError('Invalid amount');
            return;
        }

        try {
            this.setButtonLoading('stake-lp-btn', true);
            await this.simulateTransaction('Staking LP tokens...');
            
            this.lpStakingData.userStaked += amount;
            this.lpStakingData.userLPBalance -= amount;
            
            this.updateLPStakingUI();
            amountInput.value = '';
            this.showSuccess(`Successfully staked ${amount.toFixed(6)} LP tokens`);

        } catch (error) {
            this.showError('LP staking failed: ' + error.message);
        } finally {
            this.setButtonLoading('stake-lp-btn', false);
        }
    }

    async unstakeLP() {
        if (!this.checkWalletConnection()) return;

        const amountInput = document.getElementById('stake-amount-lp');
        const amount = parseFloat(amountInput.value) || 0;

        if (amount <= 0 || amount > this.lpStakingData.userStaked) {
            this.showError('Invalid amount');
            return;
        }

        try {
            this.setButtonLoading('unstake-lp-btn', true);
            await this.simulateTransaction('Unstaking LP tokens...');
            
            this.lpStakingData.userStaked -= amount;
            this.lpStakingData.userLPBalance += amount;
            
            this.updateLPStakingUI();
            amountInput.value = '';
            this.showSuccess(`Successfully unstaked ${amount.toFixed(6)} LP tokens`);

        } catch (error) {
            this.showError('LP unstaking failed: ' + error.message);
        } finally {
            this.setButtonLoading('unstake-lp-btn', false);
        }
    }

    async claimLPRewards() {
        if (!this.checkWalletConnection()) return;

        if (this.lpStakingData.pendingRewards <= 0) {
            this.showError('No rewards to claim');
            return;
        }

        try {
            this.setButtonLoading('claim-lp-btn', true);
            await this.simulateTransaction('Claiming LP rewards...');
            
            const rewards = this.lpStakingData.pendingRewards;
            this.lpStakingData.pendingRewards = 0;
            
            this.updateLPStakingUI();
            this.showSuccess(`Successfully claimed ${rewards.toFixed(2)} WTF rewards`);

        } catch (error) {
            this.showError('Claiming failed: ' + error.message);
        } finally {
            this.setButtonLoading('claim-lp-btn', false);
        }
    }

    checkWalletConnection() {
        if (!window.app || !window.app.walletConnected) {
            this.showError('Please connect your wallet first');
            return false;
        }
        return true;
    }

    async simulateTransaction(message) {
        return new Promise((resolve) => {
            setTimeout(() => {
                resolve();
            }, 2000 + Math.random() * 2000);
        });
    }

    setButtonLoading(buttonId, loading) {
        const button = document.getElementById(buttonId);
        if (!button) return;

        if (loading) {
            button.disabled = true;
            button.innerHTML = '<span class="spinner"></span> Processing...';
        } else {
            button.disabled = false;
            const originalTexts = {
                'stake-wtf-btn': 'Stake',
                'unstake-wtf-btn': 'Unstake',
                'claim-wtf-btn': 'Claim Rewards',
                'stake-lp-btn': 'Stake LP',
                'unstake-lp-btn': 'Unstake LP',
                'claim-lp-btn': 'Claim Rewards'
            };
            button.textContent = originalTexts[buttonId] || 'Submit';
        }
    }

    showLoadingState() {
        const loadingElements = document.querySelectorAll('.stake-card .stat .value');
        loadingElements.forEach(el => {
            if (!el.dataset.originalText) {
                el.dataset.originalText = el.textContent;
                el.innerHTML = '<span class="spinner"></span>';
            }
        });
    }

    hideLoadingState() {
        const loadingElements = document.querySelectorAll('.stake-card .stat .value');
        loadingElements.forEach(el => {
            if (el.dataset.originalText) {
                el.textContent = el.dataset.originalText;
                delete el.dataset.originalText;
            }
        });
    }

    formatNumber(num) {
        if (num >= 1000000) {
            return (num / 1000000).toFixed(2) + 'M';
        } else if (num >= 1000) {
            return (num / 1000).toFixed(2) + 'K';
        } else {
            return num.toFixed(2);
        }
    }

    showSuccess(message) {
        this.createNotification(message, 'success');
    }

    showError(message) {
        this.createNotification(message, 'error');
    }

    createNotification(message, type) {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.style.cssText = `
            position: fixed;
            top: 100px;
            right: 20px;
            background-color: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-left: 4px solid var(--${type === 'success' ? 'success' : 'error'});
            border-radius: 0.5rem;
            padding: 1rem;
            box-shadow: var(--shadow-lg);
            z-index: 10000;
            max-width: 300px;
            color: var(--text-primary);
        `;

        notification.textContent = message;
        document.body.appendChild(notification);

        setTimeout(() => {
            notification.remove();
        }, 5000);
    }

    initWTFStaking() {
        this.loadStakingData();
    }

    initLPStaking() {
        this.loadStakingData();
    }
}

window.StakingManager = StakingManager;
