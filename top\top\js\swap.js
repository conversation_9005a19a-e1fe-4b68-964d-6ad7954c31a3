class SwapManager {
    constructor() {
        this.tokens = [
            {
                symbol: 'ETH',
                name: 'Ethereum',
                address: '******************************************', // 1inch ETH address
                decimals: 18,
                icon: './images/ethereum-logo.png',
                balance: 0,
                coingeckoId: 'ethereum'
            },
            {
                symbol: 'WTF',
                name: 'WTF Token',
                address: '******************************************',
                decimals: 18,
                icon: './images/wtf-logo.png',
                balance: 0,
                coingeckoId: 'wtf-token'
            },
            {
                symbol: 'USDC',
                name: 'USD Coin',
                address: '******************************************',
                decimals: 6,
                icon: './images/usdc-logo.png',
                balance: 0,
                coingeckoId: 'usd-coin'
            },
            {
                symbol: 'USDT',
                name: 'Tether USD',
                address: '******************************************',
                decimals: 6,
                icon: './images/usdt-logo.png',
                balance: 0,
                coingeckoId: 'tether'
            },
            {
                symbol: 'WBTC',
                name: 'Wrapped Bitcoin',
                address: '******************************************',
                decimals: 8,
                icon: './images/wbtc-logo.png',
                balance: 0,
                coingeckoId: 'wrapped-bitcoin'
            }
        ];

        this.fromToken = this.tokens[0]; // ETH
        this.toToken = this.tokens[1];   // WTF
        this.fromAmount = 0;
        this.toAmount = 0;
        this.slippage = 0.5; // 0.5%
        this.isSwapping = false;
        this.currentQuote = null;
        this.quoteExpiry = null;
        this.maxRetries = 3;

        this.init();
    }

    init() {
        this.setupEventListeners();
        this.updateTokenBalances();
        this.updateSwapUI();
    }

    setupEventListeners() {
        const fromAmountInput = document.getElementById('from-amount');
        if (fromAmountInput) {
            fromAmountInput.addEventListener('input', (e) => {
                this.fromAmount = parseFloat(e.target.value) || 0;
                this.calculateToAmount();
            });
        }

        const fromTokenBtn = document.getElementById('from-token');
        const toTokenBtn = document.getElementById('to-token');

        if (fromTokenBtn) {
            fromTokenBtn.addEventListener('click', () => this.showTokenModal('from'));
        }
        if (toTokenBtn) {
            toTokenBtn.addEventListener('click', () => this.showTokenModal('to'));
        }

        const swapDirectionBtn = document.getElementById('swap-direction');
        if (swapDirectionBtn) {
            swapDirectionBtn.addEventListener('click', () => this.swapTokens());
        }

        const swapBtn = document.getElementById('swap-btn');
        if (swapBtn) {
            swapBtn.addEventListener('click', () => this.executeSwap());
        }

        this.setupTokenModalEvents();
    }

    setupTokenModalEvents() {
        const closeTokenModal = document.getElementById('close-token-modal');
        const tokenModalOverlay = document.getElementById('token-modal-overlay');
        const tokenSearch = document.getElementById('token-search');

        if (closeTokenModal) {
            closeTokenModal.addEventListener('click', () => this.hideTokenModal());
        }

        if (tokenModalOverlay) {
            tokenModalOverlay.addEventListener('click', (e) => {
                if (e.target === tokenModalOverlay) {
                    this.hideTokenModal();
                }
            });
        }

        if (tokenSearch) {
            tokenSearch.addEventListener('input', (e) => {
                this.filterTokens(e.target.value);
            });
        }
    }

    showTokenModal(type) {
        this.currentTokenSelection = type;
        const modal = document.getElementById('token-modal-overlay');
        if (modal) {
            modal.classList.remove('hidden');
            this.populateTokenList();
        }
    }

    hideTokenModal() {
        const modal = document.getElementById('token-modal-overlay');
        if (modal) {
            modal.classList.add('hidden');
        }
        this.currentTokenSelection = null;
    }

    populateTokenList() {
        const tokenList = document.getElementById('token-list');
        if (!tokenList) return;

        tokenList.innerHTML = '';

        this.tokens.forEach(token => {
            const tokenItem = document.createElement('div');
            tokenItem.className = 'token-item';
            tokenItem.innerHTML = `
                <img src="${token.icon}" alt="${token.symbol}" class="token-item-icon">
                <div class="token-item-info">
                    <div class="token-item-symbol">${token.symbol}</div>
                    <div class="token-item-name">${token.name}</div>
                </div>
                <div class="token-item-balance">${token.balance.toFixed(4)}</div>
            `;

            tokenItem.addEventListener('click', () => {
                this.selectToken(token);
                this.hideTokenModal();
            });

            tokenList.appendChild(tokenItem);
        });
    }

    filterTokens(searchTerm) {
        const tokenItems = document.querySelectorAll('.token-item');
        const term = searchTerm.toLowerCase();

        tokenItems.forEach(item => {
            const symbol = item.querySelector('.token-item-symbol').textContent.toLowerCase();
            const name = item.querySelector('.token-item-name').textContent.toLowerCase();
            
            if (symbol.includes(term) || name.includes(term)) {
                item.style.display = 'flex';
            } else {
                item.style.display = 'none';
            }
        });
    }

    selectToken(token) {
        if (this.currentTokenSelection === 'from') {
            if (token.symbol === this.toToken.symbol) {
                this.swapTokens();
                return;
            }
            this.fromToken = token;
        } else if (this.currentTokenSelection === 'to') {
            if (token.symbol === this.fromToken.symbol) {
                this.swapTokens();
                return;
            }
            this.toToken = token;
        }

        this.updateSwapUI();
        this.calculateToAmount();
    }

    swapTokens() {
        const temp = this.fromToken;
        this.fromToken = this.toToken;
        this.toToken = temp;

        const fromAmountInput = document.getElementById('from-amount');
        const toAmountInput = document.getElementById('to-amount');

        if (fromAmountInput && toAmountInput) {
            fromAmountInput.value = toAmountInput.value;
            this.fromAmount = parseFloat(toAmountInput.value) || 0;
        }

        this.updateSwapUI();
        this.calculateToAmount();
    }

    async calculateToAmount() {
        if (this.fromAmount <= 0) {
            this.toAmount = 0;
            this.updateToAmountUI();
            this.updateSwapInfo();
            return;
        }

        try {
            utils.showLoadingState('to-amount', 'Getting quote...');

            const quote = await utils.retryOperation(
                () => this.getSwapQuote(),
                'swap-quote',
                this.maxRetries
            );

            this.currentQuote = quote;
            this.quoteExpiry = Date.now() + 30000; // 30 seconds
            this.toAmount = parseFloat(quote.toTokenAmount) / Math.pow(10, this.toToken.decimals);

            this.updateToAmountUI();
            this.updateSwapInfo();

        } catch (error) {
            await utils.handleError(error, 'swap-quote', false);

            // Fallback to estimated rate
            const estimatedRate = await this.getEstimatedRate();
            this.toAmount = this.fromAmount * estimatedRate;
            this.updateToAmountUI();
            this.updateSwapInfo();

            utils.showUserError('Using estimated rate due to quote service issues', 'warning');
        } finally {
            utils.hideLoadingState('to-amount');
        }
    }

    async getSwapQuote() {
        const fromTokenAmount = (this.fromAmount * Math.pow(10, this.fromToken.decimals)).toString();

        // Try 1inch first, then 0x as fallback
        const apis = apiConfig.getBestAPI('swapQuote');
        let lastError;

        for (const apiName of apis) {
            try {
                switch (apiName) {
                    case 'oneinch':
                        return await this.get1inchQuote(fromTokenAmount);
                    case 'zeroex':
                        return await this.get0xQuote(fromTokenAmount);
                    default:
                        continue;
                }
            } catch (error) {
                console.warn(`Failed to get quote from ${apiName}:`, error.message);
                lastError = error;
                continue;
            }
        }

        throw lastError || new Error('All swap quote APIs failed');
    }

    async get1inchQuote(fromTokenAmount) {
        const params = {
            fromTokenAddress: this.fromToken.address,
            toTokenAddress: this.toToken.address,
            amount: fromTokenAmount,
            fromAddress: window.app?.currentAccount || '0x0000000000000000000000000000000000000000',
            slippage: this.slippage,
            disableEstimate: true
        };

        const data = await apiConfig.makeRequest('oneinch', apiConfig.endpoints.oneinch.quote, {
            params: params
        });

        if (!data || !data.toTokenAmount) {
            throw new Error('Invalid response from 1inch API');
        }

        return {
            ...data,
            source: '1inch'
        };
    }

    async get0xQuote(fromTokenAmount) {
        const params = {
            sellToken: this.fromToken.address,
            buyToken: this.toToken.address,
            sellAmount: fromTokenAmount,
            slippagePercentage: this.slippage / 100
        };

        const data = await apiConfig.makeRequest('zeroex', apiConfig.endpoints.zeroex.quote, {
            params: params
        });

        if (!data || !data.buyAmount) {
            throw new Error('Invalid response from 0x API');
        }

        return {
            toTokenAmount: data.buyAmount,
            estimatedGas: data.estimatedGas,
            gasPrice: data.gasPrice,
            source: '0x'
        };
    }

    async getEstimatedRate() {
        try {
            // Get token prices from CoinGecko as fallback
            const fromPrice = await this.getTokenPrice(this.fromToken.coingeckoId);
            const toPrice = await this.getTokenPrice(this.toToken.coingeckoId);

            if (fromPrice && toPrice) {
                return fromPrice / toPrice;
            }
        } catch (error) {
            console.warn('Failed to get estimated rate:', error);
        }

        // Ultimate fallback - return 1:1 ratio
        return 1;
    }

    async getTokenPrice(coingeckoId) {
        if (!coingeckoId) return null;

        try {
            const data = await apiConfig.makeRequest('coingecko', '/simple/price', {
                params: {
                    ids: coingeckoId,
                    vs_currencies: 'usd'
                }
            });

            return data[coingeckoId]?.usd || null;
        } catch (error) {
            console.warn(`Failed to get price for ${coingeckoId}:`, error);
            return null;
        }
    }

    updateSwapUI() {
        const fromTokenBtn = document.getElementById('from-token');
        const toTokenBtn = document.getElementById('to-token');

        if (fromTokenBtn) {
            fromTokenBtn.innerHTML = `
                <img src="${this.fromToken.icon}" alt="${this.fromToken.symbol}" class="token-icon">
                <span>${this.fromToken.symbol}</span>
                <span class="dropdown-arrow">▼</span>
            `;
        }

        if (toTokenBtn) {
            toTokenBtn.innerHTML = `
                <img src="${this.toToken.icon}" alt="${this.toToken.symbol}" class="token-icon">
                <span>${this.toToken.symbol}</span>
                <span class="dropdown-arrow">▼</span>
            `;
        }

        const fromBalance = document.getElementById('from-balance');
        const toBalance = document.getElementById('to-balance');

        if (fromBalance) {
            fromBalance.textContent = this.fromToken.balance.toFixed(4);
        }
        if (toBalance) {
            toBalance.textContent = this.toToken.balance.toFixed(4);
        }

        this.updateSwapButton();
    }

    updateToAmountUI() {
        const toAmountInput = document.getElementById('to-amount');
        if (toAmountInput) {
            toAmountInput.value = this.toAmount > 0 ? this.toAmount.toFixed(6) : '';
        }
    }

    updateSwapInfo() {
        // Calculate rate from current amounts
        const rate = this.fromAmount > 0 ? this.toAmount / this.fromAmount : 0;

        // Get price impact from quote or calculate estimate
        const priceImpact = this.currentQuote?.priceImpact || this.calculatePriceImpact();

        // Calculate minimum received with slippage
        const minReceived = this.toAmount * (1 - this.slippage / 100);

        // Get network fee from quote or estimate
        const networkFee = this.getNetworkFeeFromQuote() || this.calculateNetworkFee();

        // Update swap rate display
        const swapRate = document.getElementById('swap-rate');
        if (swapRate) {
            if (rate > 0) {
                swapRate.textContent = `1 ${this.fromToken.symbol} = ${utils.formatNumber(rate, 6)} ${this.toToken.symbol}`;
            } else {
                swapRate.textContent = `1 ${this.fromToken.symbol} = -- ${this.toToken.symbol}`;
            }
        }

        // Update price impact
        const priceImpactEl = document.getElementById('price-impact');
        if (priceImpactEl) {
            priceImpactEl.textContent = `${priceImpact.toFixed(2)}%`;
            priceImpactEl.className = 'impact-' + this.getPriceImpactLevel(priceImpact);
        }

        // Update minimum received
        const minReceivedEl = document.getElementById('min-received');
        if (minReceivedEl) {
            minReceivedEl.textContent = `${utils.formatNumber(minReceived, 6)} ${this.toToken.symbol}`;
        }

        // Update network fee
        const networkFeeEl = document.getElementById('network-fee');
        if (networkFeeEl) {
            networkFeeEl.textContent = `~${utils.formatCurrency(networkFee)}`;
        }

        // Show quote source and expiry
        this.updateQuoteInfo();
    }

    getNetworkFeeFromQuote() {
        if (!this.currentQuote) return null;

        try {
            const gasPrice = this.currentQuote.gasPrice || window.app?.gasTracker?.getCurrentGasData()?.standard || 20;
            const gasLimit = this.currentQuote.estimatedGas || 150000;
            const ethPrice = window.app?.gasTracker?.getCurrentETHPrice()?.usd || 2500;

            const feeInETH = (gasPrice * gasLimit) / 1e9;
            return feeInETH * ethPrice;
        } catch (error) {
            console.warn('Failed to calculate network fee from quote:', error);
            return null;
        }
    }

    updateQuoteInfo() {
        // Add quote source and expiry information
        const quoteInfoEl = document.getElementById('quote-info');
        if (quoteInfoEl && this.currentQuote) {
            const timeLeft = Math.max(0, this.quoteExpiry - Date.now());
            const secondsLeft = Math.floor(timeLeft / 1000);

            quoteInfoEl.innerHTML = `
                <div style="font-size: 0.75rem; color: var(--text-secondary); text-align: center;">
                    Quote from ${this.currentQuote.source} • Expires in ${secondsLeft}s
                </div>
            `;

            if (secondsLeft <= 0) {
                quoteInfoEl.innerHTML += `
                    <div style="font-size: 0.75rem; color: var(--warning); text-align: center;">
                        Quote expired - refresh for new rate
                    </div>
                `;
            }
        }
    }

    calculatePriceImpact() {
        const tradeSize = this.fromAmount;
        if (tradeSize < 1) return 0.01;
        if (tradeSize < 10) return 0.1;
        if (tradeSize < 100) return 0.5;
        return 1.0;
    }

    getPriceImpactLevel(impact) {
        if (impact < 0.1) return 'low';
        if (impact < 1.0) return 'medium';
        return 'high';
    }

    calculateNetworkFee() {
        const gasPrice = window.app?.gasTracker?.getCurrentGasData()?.standard || 20;
        const gasLimit = 150000;
        const ethPrice = window.app?.gasTracker?.getCurrentETHPrice()?.usd || 2500;
        
        const feeInETH = (gasPrice * gasLimit) / 1e9;
        return feeInETH * ethPrice;
    }

    updateSwapButton() {
        const swapBtn = document.getElementById('swap-btn');
        if (!swapBtn) return;

        if (!window.app || !window.app.walletConnected) {
            swapBtn.textContent = 'Connect Wallet';
            swapBtn.disabled = false;
            return;
        }

        if (this.fromAmount <= 0) {
            swapBtn.textContent = 'Enter Amount';
            swapBtn.disabled = true;
            return;
        }

        if (this.fromAmount > this.fromToken.balance) {
            swapBtn.textContent = `Insufficient ${this.fromToken.symbol} Balance`;
            swapBtn.disabled = true;
            return;
        }

        swapBtn.textContent = `Swap ${this.fromToken.symbol} for ${this.toToken.symbol}`;
        swapBtn.disabled = false;
    }

    async executeSwap() {
        if (!window.app || !window.app.walletConnected) {
            window.app.showWalletModal();
            return;
        }

        if (this.fromAmount <= 0 || this.fromAmount > this.fromToken.balance) {
            this.showError('Invalid swap amount');
            return;
        }

        try {
            this.isSwapping = true;
            this.setSwapButtonLoading(true);

            await this.simulateSwapTransaction();

            this.fromToken.balance -= this.fromAmount;
            this.toToken.balance += this.toAmount;

            const fromAmountInput = document.getElementById('from-amount');
            if (fromAmountInput) {
                fromAmountInput.value = '';
            }
            this.fromAmount = 0;
            this.toAmount = 0;

            this.updateSwapUI();
            this.updateToAmountUI();

            this.showSuccess(`Successfully swapped ${this.fromAmount} ${this.fromToken.symbol} for ${this.toAmount.toFixed(4)} ${this.toToken.symbol}`);

        } catch (error) {
            console.error('Swap failed:', error);
            this.showError('Swap failed: ' + error.message);
        } finally {
            this.isSwapping = false;
            this.setSwapButtonLoading(false);
        }
    }

    async simulateSwapTransaction() {
        return new Promise((resolve, reject) => {
            setTimeout(() => {
                if (Math.random() < 0.1) {
                    reject(new Error('Transaction failed'));
                } else {
                    resolve();
                }
            }, 3000 + Math.random() * 2000);
        });
    }

    setSwapButtonLoading(loading) {
        const swapBtn = document.getElementById('swap-btn');
        if (!swapBtn) return;

        if (loading) {
            swapBtn.innerHTML = '<span class="spinner"></span> Swapping...';
            swapBtn.disabled = true;
        } else {
            this.updateSwapButton();
        }
    }

    async updateTokenBalances() {
        if (!window.app || !window.app.walletConnected) {
            // Set demo balances for disconnected state
            this.tokens[0].balance = 1.5; // ETH
            this.tokens[1].balance = 1000; // WTF
            this.tokens[2].balance = 5000; // USDC
            this.tokens[3].balance = 2500; // USDT
            this.tokens[4].balance = 0.05; // WBTC
            return;
        }

        try {
            utils.startPerformanceTimer('token-balances-fetch');

            const userAddress = window.app.currentAccount;
            const balancePromises = this.tokens.map(token =>
                this.getTokenBalance(token, userAddress)
            );

            const balances = await Promise.allSettled(balancePromises);

            balances.forEach((result, index) => {
                if (result.status === 'fulfilled') {
                    this.tokens[index].balance = result.value;
                } else {
                    console.warn(`Failed to fetch balance for ${this.tokens[index].symbol}:`, result.reason);
                    // Keep previous balance or set to 0
                    this.tokens[index].balance = this.tokens[index].balance || 0;
                }
            });

            this.updateSwapUI();

        } catch (error) {
            await utils.handleError(error, 'token-balances-fetch');
        } finally {
            utils.endPerformanceTimer('token-balances-fetch');
        }
    }

    async getTokenBalance(token, userAddress) {
        try {
            if (token.symbol === 'ETH') {
                return await this.getETHBalance(userAddress);
            } else {
                return await this.getERC20Balance(token, userAddress);
            }
        } catch (error) {
            console.warn(`Failed to get balance for ${token.symbol}:`, error);
            return 0;
        }
    }

    async getETHBalance(userAddress) {
        if (!window.ethereum) {
            throw new Error('No Web3 provider available');
        }

        try {
            const balance = await window.ethereum.request({
                method: 'eth_getBalance',
                params: [userAddress, 'latest']
            });

            // Convert from wei to ETH
            return parseInt(balance, 16) / Math.pow(10, 18);
        } catch (error) {
            // Fallback to Alchemy/Infura if direct call fails
            return await this.getETHBalanceFromAPI(userAddress);
        }
    }

    async getETHBalanceFromAPI(userAddress) {
        const data = await apiConfig.makeRequest('alchemy', '/eth_getBalance', {
            method: 'POST',
            body: {
                jsonrpc: '2.0',
                id: 1,
                method: 'eth_getBalance',
                params: [userAddress, 'latest']
            }
        });

        if (data.error) {
            throw new Error(data.error.message);
        }

        return parseInt(data.result, 16) / Math.pow(10, 18);
    }

    async getERC20Balance(token, userAddress) {
        if (!window.ethereum) {
            throw new Error('No Web3 provider available');
        }

        try {
            // ERC20 balanceOf function call
            const data = '0x70a08231' + userAddress.slice(2).padStart(64, '0');

            const result = await window.ethereum.request({
                method: 'eth_call',
                params: [{
                    to: token.address,
                    data: data
                }, 'latest']
            });

            if (result === '0x') {
                return 0;
            }

            const balance = parseInt(result, 16);
            return balance / Math.pow(10, token.decimals);

        } catch (error) {
            console.warn(`Failed to get ${token.symbol} balance via Web3:`, error);
            // Could implement API fallback here if needed
            return 0;
        }
    }

    showSuccess(message) {
        this.createNotification(message, 'success');
    }

    showError(message) {
        this.createNotification(message, 'error');
    }

    createNotification(message, type) {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.style.cssText = `
            position: fixed;
            top: 100px;
            right: 20px;
            background-color: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-left: 4px solid var(--${type === 'success' ? 'success' : 'error'});
            border-radius: 0.5rem;
            padding: 1rem;
            box-shadow: var(--shadow-lg);
            z-index: 10000;
            max-width: 300px;
            color: var(--text-primary);
        `;

        notification.textContent = message;
        document.body.appendChild(notification);

        setTimeout(() => {
            notification.remove();
        }, 5000);
    }

    // Public methods
    initSwap() {
        this.updateTokenBalances();
        this.updateSwapUI();
    }

    getTokenBySymbol(symbol) {
        return this.tokens.find(token => token.symbol === symbol);
    }

    addToken(tokenData) {
        this.tokens.push(tokenData);
    }
}

window.SwapManager = SwapManager;
