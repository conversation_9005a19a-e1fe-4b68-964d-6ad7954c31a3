class GasTracker {
    constructor() {
        this.gasData = {
            fast: 0,
            standard: 0,
            safe: 0,
            timestamp: null,
            source: null
        };

        this.ethPrice = {
            usd: 0,
            change24h: 0,
            timestamp: null,
            source: null
        };

        this.updateInterval = 30000; // 30 seconds
        this.priceUpdateInterval = 60000; // 1 minute
        this.maxRetries = 3;
        this.fallbackData = null;

        this.init();
    }

    init() {
        this.startTracking();
        this.setupEventListeners();
    }

    setupEventListeners() {
        const gasTracker = document.querySelector('.gas-tracker');
        if (gasTracker) {
            gasTracker.addEventListener('click', () => {
                this.showGasDetails();
            });
        }
    }

    startTracking() {
        this.updateGasData();
        this.updateETHPrice();
        
        setInterval(() => this.updateGasData(), this.updateInterval);
        setInterval(() => this.updateETHPrice(), this.priceUpdateInterval);
    }

    async updateGasData() {
        utils.startPerformanceTimer('gas-data-fetch');

        try {
            const gasData = await utils.retryOperation(
                () => this.fetchGasData(),
                'gas-data-fetch',
                this.maxRetries
            );

            this.gasData = {
                ...gasData,
                timestamp: new Date()
            };

            // Store as fallback
            this.fallbackData = { ...this.gasData };
            localStorage.setItem('gasDataFallback', JSON.stringify(this.fallbackData));

            this.updateGasUI();
            this.notifyGasChange();

        } catch (error) {
            await utils.handleError(error, 'gas-data-fetch');
            this.handleGasError();
        } finally {
            utils.endPerformanceTimer('gas-data-fetch');
        }
    }

    async fetchGasData() {
        const apis = apiConfig.getBestAPI('gasPrice');
        let lastError;

        for (const apiName of apis) {
            try {
                switch (apiName) {
                    case 'etherscan':
                        return await this.fetchEtherscanGasData();
                    case 'ethGasStation':
                        return await this.fetchEthGasStationData();
                    default:
                        continue;
                }
            } catch (error) {
                console.warn(`Failed to fetch gas data from ${apiName}:`, error.message);
                lastError = error;
                continue;
            }
        }

        // Try fallback data if all APIs fail
        const fallback = this.getFallbackGasData();
        if (fallback) {
            utils.showUserError('Using cached gas data due to API issues', 'warning');
            return fallback;
        }

        throw lastError || new Error('All gas price APIs failed');
    }

    async fetchEtherscanGasData() {
        const data = await apiConfig.makeRequest('etherscan', apiConfig.endpoints.etherscan.gasPrice);

        if (data.status !== '1' || !data.result) {
            throw new Error('Invalid response from Etherscan API');
        }

        const result = data.result;
        return {
            safe: parseInt(result.SafeGasPrice) || 10,
            standard: parseInt(result.StandardGasPrice) || 20,
            fast: parseInt(result.FastGasPrice) || 30,
            source: 'etherscan'
        };
    }

    async fetchEthGasStationData() {
        const data = await apiConfig.makeRequest('ethGasStation', apiConfig.endpoints.ethGasStation.gasPrice);

        if (!data || typeof data !== 'object') {
            throw new Error('Invalid response from ETH Gas Station API');
        }

        return {
            safe: Math.round((data.safeLow || 100) / 10), // Convert from 0.1 gwei to gwei
            standard: Math.round((data.standard || 200) / 10),
            fast: Math.round((data.fast || 300) / 10),
            source: 'ethGasStation'
        };
    }

    getFallbackGasData() {
        try {
            const stored = localStorage.getItem('gasDataFallback');
            if (stored) {
                const fallback = JSON.parse(stored);
                // Only use if less than 10 minutes old
                if (Date.now() - new Date(fallback.timestamp).getTime() < 600000) {
                    return {
                        safe: fallback.safe,
                        standard: fallback.standard,
                        fast: fallback.fast,
                        source: 'fallback'
                    };
                }
            }
        } catch (error) {
            console.warn('Failed to load fallback gas data:', error);
        }

        return null;
    }

    async updateETHPrice() {
        utils.startPerformanceTimer('eth-price-fetch');

        try {
            const priceData = await utils.retryOperation(
                () => this.fetchETHPrice(),
                'eth-price-fetch',
                this.maxRetries
            );

            this.ethPrice = {
                ...priceData,
                timestamp: new Date()
            };

            // Store as fallback
            localStorage.setItem('ethPriceFallback', JSON.stringify(this.ethPrice));

            this.updateETHPriceUI();

        } catch (error) {
            await utils.handleError(error, 'eth-price-fetch');
            this.handlePriceError();
        } finally {
            utils.endPerformanceTimer('eth-price-fetch');
        }
    }

    async fetchETHPrice() {
        const apis = apiConfig.getBestAPI('ethPrice');
        let lastError;

        for (const apiName of apis) {
            try {
                switch (apiName) {
                    case 'coingecko':
                        return await this.fetchCoinGeckoPrice();
                    case 'coinmarketcap':
                        return await this.fetchCoinMarketCapPrice();
                    default:
                        continue;
                }
            } catch (error) {
                console.warn(`Failed to fetch ETH price from ${apiName}:`, error.message);
                lastError = error;
                continue;
            }
        }

        // Try fallback data if all APIs fail
        const fallback = this.getFallbackETHPrice();
        if (fallback) {
            utils.showUserError('Using cached ETH price due to API issues', 'warning');
            return fallback;
        }

        throw lastError || new Error('All ETH price APIs failed');
    }

    async fetchCoinGeckoPrice() {
        const data = await apiConfig.makeRequest('coingecko', apiConfig.endpoints.coingecko.ethPrice);

        if (!data || !data.ethereum) {
            throw new Error('Invalid response from CoinGecko API');
        }

        const ethData = data.ethereum;
        return {
            usd: ethData.usd || 0,
            change24h: ethData.usd_24h_change || 0,
            source: 'coingecko'
        };
    }

    async fetchCoinMarketCapPrice() {
        const data = await apiConfig.makeRequest('coinmarketcap', '/cryptocurrency/quotes/latest', {
            params: { symbol: 'ETH', convert: 'USD' }
        });

        if (!data || !data.data || !data.data.ETH) {
            throw new Error('Invalid response from CoinMarketCap API');
        }

        const ethData = data.data.ETH;
        const quote = ethData.quote.USD;

        return {
            usd: quote.price || 0,
            change24h: quote.percent_change_24h || 0,
            source: 'coinmarketcap'
        };
    }

    getFallbackETHPrice() {
        try {
            const stored = localStorage.getItem('ethPriceFallback');
            if (stored) {
                const fallback = JSON.parse(stored);
                // Only use if less than 5 minutes old
                if (Date.now() - new Date(fallback.timestamp).getTime() < 300000) {
                    return {
                        usd: fallback.usd,
                        change24h: fallback.change24h,
                        source: 'fallback'
                    };
                }
            }
        } catch (error) {
            console.warn('Failed to load fallback ETH price:', error);
        }

        return null;
    }

    updateGasUI() {
        const currentGasEl = document.getElementById('current-gas');
        if (currentGasEl) {
            currentGasEl.textContent = this.gasData.standard;
        }

        const fastGasEl = document.getElementById('fast-gas');
        const standardGasEl = document.getElementById('standard-gas');
        const safeGasEl = document.getElementById('safe-gas');

        if (fastGasEl) fastGasEl.textContent = `${this.gasData.fast} gwei`;
        if (standardGasEl) standardGasEl.textContent = `${this.gasData.standard} gwei`;
        if (safeGasEl) safeGasEl.textContent = `${this.gasData.safe} gwei`;

        this.updateGasIndicators();
    }

    updateGasIndicators() {
        const indicators = document.querySelectorAll('.gas-indicator');
        indicators.forEach(indicator => {
            const gasValue = parseInt(indicator.textContent);
            indicator.className = 'gas-indicator';
            
            if (gasValue < 20) {
                indicator.classList.add('low');
            } else if (gasValue < 50) {
                indicator.classList.add('medium');
            } else {
                indicator.classList.add('high');
            }
        });
    }

    updateETHPriceUI() {
        const ethPriceEl = document.getElementById('eth-price');
        const ethChangeEl = document.getElementById('eth-change');

        if (ethPriceEl) {
            ethPriceEl.textContent = `$${this.ethPrice.usd.toLocaleString()}`;
        }

        if (ethChangeEl) {
            const change = this.ethPrice.change24h;
            const sign = change >= 0 ? '+' : '';
            ethChangeEl.textContent = `${sign}${change.toFixed(2)}%`;
            
            ethChangeEl.className = 'stat-change';
            if (change > 0) {
                ethChangeEl.classList.add('positive');
            } else if (change < 0) {
                ethChangeEl.classList.add('negative');
            }
        }
    }

    notifyGasChange() {
        const previousGas = localStorage.getItem('previousGasPrice');
        const currentGas = this.gasData.standard;
        
        if (previousGas) {
            const change = ((currentGas - previousGas) / previousGas) * 100;
            
            if (Math.abs(change) > 20) {
                this.showGasNotification(change, currentGas);
            }
        }
        
        localStorage.setItem('previousGasPrice', currentGas);
    }

    showGasNotification(changePercent, currentGas) {
        const isIncrease = changePercent > 0;
        const message = `Gas prices ${isIncrease ? 'increased' : 'decreased'} by ${Math.abs(changePercent).toFixed(1)}% to ${currentGas} gwei`;
        
        this.createNotification(message, isIncrease ? 'warning' : 'success');
    }

    showGasDetails() {
        const modal = this.createGasModal();
        document.body.appendChild(modal);
        
        setTimeout(() => {
            modal.remove();
        }, 5000);
    }

    createGasModal() {
        const modal = document.createElement('div');
        modal.className = 'gas-modal';
        modal.style.cssText = `
            position: fixed;
            top: 80px;
            right: 20px;
            background-color: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 0.75rem;
            padding: 1.5rem;
            box-shadow: var(--shadow-lg);
            z-index: 1001;
            min-width: 250px;
        `;

        modal.innerHTML = `
            <h3 style="margin: 0 0 1rem 0; color: var(--text-primary);">Gas Prices</h3>
            <div style="display: flex; flex-direction: column; gap: 0.75rem;">
                <div style="display: flex; justify-content: space-between;">
                    <span style="color: var(--text-secondary);">🐌 Safe:</span>
                    <span style="color: var(--success); font-weight: 600;">${this.gasData.safe} gwei</span>
                </div>
                <div style="display: flex; justify-content: space-between;">
                    <span style="color: var(--text-secondary);">🚀 Standard:</span>
                    <span style="color: var(--warning); font-weight: 600;">${this.gasData.standard} gwei</span>
                </div>
                <div style="display: flex; justify-content: space-between;">
                    <span style="color: var(--text-secondary);">⚡ Fast:</span>
                    <span style="color: var(--error); font-weight: 600;">${this.gasData.fast} gwei</span>
                </div>
            </div>
            <div style="margin-top: 1rem; padding-top: 1rem; border-top: 1px solid var(--border-color);">
                <small style="color: var(--text-secondary);">
                    Last updated: ${this.gasData.timestamp ? this.gasData.timestamp.toLocaleTimeString() : 'Never'}
                </small>
            </div>
        `;

        modal.addEventListener('click', () => modal.remove());

        return modal;
    }

    createNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.style.cssText = `
            position: fixed;
            top: 100px;
            right: 20px;
            background-color: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-left: 4px solid var(--${type === 'success' ? 'success' : type === 'warning' ? 'warning' : 'info'});
            border-radius: 0.5rem;
            padding: 1rem;
            box-shadow: var(--shadow-lg);
            z-index: 10000;
            max-width: 300px;
            color: var(--text-primary);
        `;

        notification.textContent = message;
        document.body.appendChild(notification);

        setTimeout(() => {
            notification.remove();
        }, 5000);
    }

    handleGasError() {
        // Try to use fallback data first
        const fallback = this.getFallbackGasData();
        if (fallback) {
            this.gasData = {
                ...fallback,
                timestamp: new Date()
            };
            this.updateGasUI();
            return;
        }

        // If no fallback, show error state
        const currentGasEl = document.getElementById('current-gas');
        if (currentGasEl) {
            currentGasEl.textContent = '--';
        }

        const fastGasEl = document.getElementById('fast-gas');
        const standardGasEl = document.getElementById('standard-gas');
        const safeGasEl = document.getElementById('safe-gas');

        if (fastGasEl) fastGasEl.textContent = '-- gwei';
        if (standardGasEl) standardGasEl.textContent = '-- gwei';
        if (safeGasEl) safeGasEl.textContent = '-- gwei';
    }

    handlePriceError() {
        // Try to use fallback data first
        const fallback = this.getFallbackETHPrice();
        if (fallback) {
            this.ethPrice = {
                ...fallback,
                timestamp: new Date()
            };
            this.updateETHPriceUI();
            return;
        }

        // If no fallback, show error state
        const ethPriceEl = document.getElementById('eth-price');
        const ethChangeEl = document.getElementById('eth-change');

        if (ethPriceEl) ethPriceEl.textContent = '$--';
        if (ethChangeEl) ethChangeEl.textContent = '--';
    }

    getCurrentGasData() {
        return { ...this.gasData };
    }

    getCurrentETHPrice() {
        return { ...this.ethPrice };
    }

    calculateTransactionCost(gasLimit, gasPrice = null) {
        const price = gasPrice || this.gasData.standard;
        const costInGwei = gasLimit * price;
        const costInETH = costInGwei / 1e9;
        const costInUSD = costInETH * this.ethPrice.usd;
        
        return {
            gwei: costInGwei,
            eth: costInETH,
            usd: costInUSD
        };
    }
}

window.GasTracker = GasTracker;
